# 🧪 特定物质安全性信息爬虫

## 📋 项目概述

这是一个专门收集**特定物质在药品中的安全性信息**的爬虫系统，重新定位分析目标：

- **旧目标**：收集药品的整体安全性信息
- **新目标**：收集特定物质（检索词）在各个药品中的用量、浓度和安全性描述

## 🎯 核心功能

### 📊 数据收集目标
1. **药品识别信息**：
   - 药品唯一标识符（SetID）
   - NDC号码
   - 药品名称
   - 剂型（Tablet, Injection, Capsule等）
   - 制造商信息

2. **物质特定信息**：
   - 物质用量/含量（如"250mg"、"5%"等）
   - 物质浓度信息
   - 物质在该药品中的角色（活性成分/非活性成分）
   - 物质特定的安全性描述（如果存在）

3. **质量保证**：
   - 信息提取来源标记
   - 置信度评分
   - 智能剂型识别

## 🚀 使用方法

### 🎯 智能模式（推荐）
```bash
# 只提供物质名称，自动查找UNII代码
python substance_safety_crawler.py --substance "trehalose dihydrate"

# 只提供UNII代码，自动查找物质名称
python substance_safety_crawler.py --substance "7YIN7J07X4"

# 收集其他物质的信息
python substance_safety_crawler.py --substance "alcohol"
python substance_safety_crawler.py --substance "sodium chloride"
```

### ⚙️ 高级配置
```bash
# 自定义参数
python substance_safety_crawler.py \
  --substance "trehalose dihydrate" \
  --concurrent 6 \
  --max-pages 5 \
  --output "trehalose_analysis"

# 详细日志输出
python substance_safety_crawler.py --substance "alcohol" --verbose
```

### 🗄️ 首次使用准备
```bash
# 下载并初始化本地UNII数据库（首次使用必须）
python unii_database.py --download --load

# 查看数据库信息
python unii_database.py --info
```

## 📊 输出格式

### CSV文件列说明
| 列名 | 说明 | 示例 |
|------|------|------|
| 药品唯一标识符 | FDA SetID | ef290433-997f-4e98-86d6-42f6a99d6d18 |
| NDC号码 | 国家药品代码 | 0944-4622-01 |
| 药品名称 | 完整药品名称 | ADYNOVATE (antihemophilic factor...) |
| 剂型 | 标准化剂型 | Injection, Tablet, Capsule |
| 制造商 | 制造商信息 | Pfizer Inc. |
| 物质用量 | 具体用量 | 250 mg, 5%, 1.6 mg |
| 物质浓度 | 浓度信息 | 10 mg/ml |
| 物质安全性信息 | 特定安全性描述 | 如果存在相关描述 |
| 物质角色 | 成分角色 | 活性成分, 非活性成分 |
| 信息来源 | 提取来源 | ingredient_table, description_section |
| 置信度 | 信息可靠性 | 90.0%, 70.0%, 50.0% |

### JSON文件结构
```json
{
  "search_info": {
    "search_term": "trehalose dihydrate",
    "unii_code": "7YIN7J07X4",
    "total_records": 40,
    "extraction_time": "2025-09-03T22:18:06"
  },
  "substance_data": [
    {
      "drug_id": "ef290433-997f-4e98-86d6-42f6a99d6d18",
      "drug_name": "ADYNOVATE...",
      "substance_amount": "53 L",
      "dosage_form": "Kit",
      "confidence_score": 0.9
    }
  ]
}
```

## 📈 实际测试结果

### 海藻糖二水合物 (TREHALOSE DIHYDRATE) 测试
- **搜索结果**: 40个药品
- **用量信息覆盖率**: 77.5%
- **置信度**: 100%高置信度记录
- **主要剂型**: 注射剂(20个)、粉剂(8个)、试剂盒(8个)

### 关键发现
1. **用量范围**: 从1.6mg到910mg不等
2. **剂型分布**: 主要用于注射剂和粉剂
3. **角色定位**: 全部作为活性成分使用
4. **信息来源**: 主要从成分表格中提取

## 🌟 核心优势

### 🎯 **精准定位**
- 专注于特定物质，而非药品整体
- 提取物质在每个药品中的具体用量
- 识别物质的功能角色

### 🧠 **智能识别**
- 自动UNII代码解析
- 智能剂型识别
- 多源信息整合

### 📊 **数据质量**
- 置信度评分系统
- 信息来源追踪
- 统计分析报告

### ⚡ **高效处理**
- 并发处理提升速度
- 智能错误处理
- 进度实时监控

## 🔍 与传统方法的区别

| 维度 | 传统药品安全性爬虫 | 特定物质安全性爬虫 |
|------|-------------------|-------------------|
| **分析目标** | 药品的整体安全性 | 特定物质的使用信息 |
| **数据焦点** | 禁忌症、不良反应等 | 用量、浓度、角色 |
| **应用场景** | 临床用药指导 | 物质安全性评估 |
| **数据粒度** | 药品级别 | 成分级别 |
| **信息深度** | 广泛但通用 | 专精且具体 |

## 📁 项目文件

```
📦 特定物质安全性信息爬虫/
├── 📄 SUBSTANCE_SAFETY_README.md    # 本说明文档
├── 🐍 substance_safety_crawler.py   # 主爬虫程序
├── 🗄️ unii_database.py             # UNII数据库管理工具
├── 💾 unii_database.db             # 本地UNII数据库
└── 📊 test_substance_crawler/       # 测试结果示例
    ├── CSV数据文件
    ├── JSON详细数据
    └── 统计分析文件
```

## 🎯 应用场景

### 🔬 **科研应用**
- 物质在不同药品中的用量分析
- 剂型与用量关系研究
- 物质使用模式统计

### 💊 **监管应用**
- 特定物质的市场使用情况
- 用量范围和安全性评估
- 制造商使用模式分析

### 📊 **商业应用**
- 竞品分析和市场调研
- 配方优化参考
- 供应链风险评估

---

**版本**: 1.0  
**最后更新**: 2025年9月3日  
**特色**: 专注特定物质 + 智能UNII解析 + 剂型识别 + 用量提取  
**验证状态**: ✅ 通过海藻糖二水合物实际测试，77.5%用量信息覆盖率

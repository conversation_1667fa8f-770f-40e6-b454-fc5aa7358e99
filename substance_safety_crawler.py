#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特定物质安全性信息爬虫 - 专注于特定物质在药品中的安全性信息
重新定位：收集特定物质（检索词）在各个药品中的用量、浓度和安全性描述
"""

import asyncio
import aiohttp
import json
import csv
import time
import logging
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Set, Tuple
from urllib.parse import urljoin, quote
from dataclasses import dataclass
import pandas as pd
import re
from bs4 import BeautifulSoup
import sqlite3
from googletrans import Translator
import urllib.parse
import langdetect

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('substance_safety_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class ChineseTranslator:
    """中文翻译支持类"""

    def __init__(self):
        self.translator = Translator()
        # 常见中文化学物质名称映射
        self.chinese_mappings = {
            '海藻糖二水合物': 'trehalose dihydrate',
            '海藻糖': 'trehalose',
            '酒精': 'alcohol',
            '乙醇': 'ethanol',
            '水': 'water',
            '氯化钠': 'sodium chloride',
            '葡萄糖': 'glucose',
            '蔗糖': 'sucrose',
            '乳糖': 'lactose',
            '甘露醇': 'mannitol',
            '山梨醇': 'sorbitol',
            '甘油': 'glycerin',
            '丙二醇': 'propylene glycol',
            '聚乙二醇': 'polyethylene glycol',
            '苯甲醇': 'benzyl alcohol',
            '异丙醇': 'isopropyl alcohol',
            '甲醇': 'methanol',
            '丙酮': 'acetone',
            '咖啡因': 'caffeine',
            '阿司匹林': 'aspirin',
            '布洛芬': 'ibuprofen',
            '对乙酰氨基酚': 'acetaminophen',
            '利多卡因': 'lidocaine',
            '苯佐卡因': 'benzocaine',
            '薄荷醇': 'menthol',
            '樟脑': 'camphor',
            '水杨酸': 'salicylic acid',
            '苯甲酸': 'benzoic acid',
            '柠檬酸': 'citric acid',
            '抗坏血酸': 'ascorbic acid',
            '维生素E': 'vitamin e',
            '视黄醇': 'retinol',
            '胆固醇': 'cholesterol'
        }

    def detect_language(self, text: str) -> str:
        """检测文本语言"""
        try:
            # 检查是否包含中文字符
            if re.search(r'[\u4e00-\u9fff]', text):
                return 'zh'

            # 使用langdetect进行语言检测
            detected = langdetect.detect(text)
            return detected
        except Exception as e:
            logger.warning(f"语言检测失败: {e}")
            return 'en'  # 默认为英文

    def translate_to_english(self, chinese_text: str) -> str:
        """将中文翻译为英文"""
        try:
            # 首先检查预定义映射
            chinese_text_clean = chinese_text.strip().lower()
            for chinese, english in self.chinese_mappings.items():
                if chinese in chinese_text_clean:
                    logger.info(f"🔄 使用预定义映射: {chinese_text} -> {english}")
                    return english

            # 使用Google翻译
            result = self.translator.translate(chinese_text, src='zh', dest='en')
            translated = result.text.lower().strip()
            logger.info(f"🔄 Google翻译: {chinese_text} -> {translated}")
            return translated

        except Exception as e:
            logger.error(f"翻译失败: {e}")
            return chinese_text  # 翻译失败时返回原文

    def process_search_term(self, search_term: str) -> str:
        """处理搜索词，如果是中文则翻译为英文"""
        language = self.detect_language(search_term)

        if language == 'zh':
            logger.info(f"🌏 检测到中文输入: {search_term}")
            english_term = self.translate_to_english(search_term)
            return english_term
        else:
            logger.info(f"🌏 检测到英文输入: {search_term}")
            return search_term

class AdvancedFilter:
    """高级搜索过滤功能类"""

    def __init__(self):
        self.available_filters = {
            'dosage_form': ['Tablet', 'Capsule', 'Injection', 'Solution', 'Kit', 'Powder', 'Cream', 'Ointment', 'Gel', 'Patch', 'Inhaler', 'Suppository'],
            'route_of_administration': ['Oral', 'Intravenous', 'Intramuscular', 'Subcutaneous', 'Topical', 'Inhalation', 'Ophthalmic', 'Nasal', 'Rectal', 'Vaginal', 'Transdermal'],
            'substance_role': ['活性成分', '非活性成分', '成分'],
            'extraction_source': ['ingredients_and_appearance', 'page_mention'],
            'confidence_range': ['high', 'medium', 'low']  # ≥70%, 30-70%, <30%
        }

    def parse_strength_range(self, strength_filter: str) -> Tuple[Optional[float], Optional[float]]:
        """解析用量范围过滤条件"""
        try:
            if '-' in strength_filter:
                # 范围格式: "1-100" 或 "1mg-100mg"
                parts = strength_filter.split('-')
                min_val = self._extract_numeric_value(parts[0])
                max_val = self._extract_numeric_value(parts[1])
                return min_val, max_val
            elif strength_filter.startswith('>'):
                # 大于格式: ">50"
                min_val = self._extract_numeric_value(strength_filter[1:])
                return min_val, None
            elif strength_filter.startswith('<'):
                # 小于格式: "<100"
                max_val = self._extract_numeric_value(strength_filter[1:])
                return None, max_val
            else:
                # 精确值: "50"
                val = self._extract_numeric_value(strength_filter)
                return val, val
        except Exception as e:
            logger.warning(f"解析用量范围失败: {e}")
            return None, None

    def _extract_numeric_value(self, text: str) -> Optional[float]:
        """从文本中提取数值"""
        try:
            # 提取数字部分
            match = re.search(r'(\d+(?:\.\d+)?)', text.strip())
            if match:
                return float(match.group(1))
        except Exception:
            pass
        return None

    def filter_results(self, results: List[SubstanceInfo], filters: Dict) -> List[SubstanceInfo]:
        """根据过滤条件筛选结果"""
        filtered_results = results.copy()

        # 剂型过滤
        if filters.get('dosage_form'):
            dosage_forms = [df.strip() for df in filters['dosage_form'].split(',')]
            filtered_results = [r for r in filtered_results if any(df.lower() in r.dosage_form.lower() for df in dosage_forms)]

        # 给药方式过滤
        if filters.get('route_of_administration'):
            routes = [route.strip() for route in filters['route_of_administration'].split(',')]
            filtered_results = [r for r in filtered_results if any(route.lower() in r.route_of_administration.lower() for route in routes)]

        # 物质角色过滤
        if filters.get('substance_role'):
            roles = [role.strip() for role in filters['substance_role'].split(',')]
            filtered_results = [r for r in filtered_results if any(role in r.substance_role for role in roles)]

        # 置信度范围过滤
        if filters.get('confidence_range'):
            confidence_filter = filters['confidence_range'].lower()
            if confidence_filter == 'high':
                filtered_results = [r for r in filtered_results if r.confidence_score >= 0.7]
            elif confidence_filter == 'medium':
                filtered_results = [r for r in filtered_results if 0.3 <= r.confidence_score < 0.7]
            elif confidence_filter == 'low':
                filtered_results = [r for r in filtered_results if r.confidence_score < 0.3]

        # 用量范围过滤
        if filters.get('strength_range'):
            min_val, max_val = self.parse_strength_range(filters['strength_range'])
            if min_val is not None or max_val is not None:
                filtered_results = self._filter_by_strength(filtered_results, min_val, max_val)

        # 药品名称模糊匹配
        if filters.get('drug_name_pattern'):
            pattern = filters['drug_name_pattern']
            if filters.get('use_regex', False):
                try:
                    regex = re.compile(pattern, re.IGNORECASE)
                    filtered_results = [r for r in filtered_results if regex.search(r.drug_name)]
                except re.error:
                    logger.warning(f"无效的正则表达式: {pattern}")
            else:
                filtered_results = [r for r in filtered_results if pattern.lower() in r.drug_name.lower()]

        return filtered_results

    def _filter_by_strength(self, results: List[SubstanceInfo], min_val: Optional[float], max_val: Optional[float]) -> List[SubstanceInfo]:
        """根据用量范围过滤结果"""
        filtered = []
        for result in results:
            strength_val = self._extract_numeric_value(result.substance_strength)
            if strength_val is not None:
                if min_val is not None and strength_val < min_val:
                    continue
                if max_val is not None and strength_val > max_val:
                    continue
                filtered.append(result)
        return filtered

    def get_filter_statistics(self, results: List[SubstanceInfo]) -> Dict:
        """获取过滤统计信息"""
        stats = {
            'total_count': len(results),
            'dosage_forms': {},
            'routes': {},
            'roles': {},
            'confidence_distribution': {'high': 0, 'medium': 0, 'low': 0},
            'strength_range': {'min': None, 'max': None, 'with_strength': 0}
        }

        strength_values = []

        for result in results:
            # 剂型统计
            form = result.dosage_form or '未知'
            stats['dosage_forms'][form] = stats['dosage_forms'].get(form, 0) + 1

            # 给药方式统计
            route = result.route_of_administration or '未知'
            stats['routes'][route] = stats['routes'].get(route, 0) + 1

            # 物质角色统计
            role = result.substance_role or '未知'
            stats['roles'][role] = stats['roles'].get(role, 0) + 1

            # 置信度统计
            if result.confidence_score >= 0.7:
                stats['confidence_distribution']['high'] += 1
            elif result.confidence_score >= 0.3:
                stats['confidence_distribution']['medium'] += 1
            else:
                stats['confidence_distribution']['low'] += 1

            # 用量统计
            strength_val = self._extract_numeric_value(result.substance_strength)
            if strength_val is not None:
                strength_values.append(strength_val)
                stats['strength_range']['with_strength'] += 1

        if strength_values:
            stats['strength_range']['min'] = min(strength_values)
            stats['strength_range']['max'] = max(strength_values)

        return stats

@dataclass
class SubstanceInfo:
    """物质信息数据类"""
    drug_id: str                    # 药品唯一标识符（SetID）
    ndc_number: str                 # NDC号码
    drug_name: str                  # 药品名称
    dosage_form: str                # 剂型
    route_of_administration: str    # 给药途径
    manufacturer: str               # 制造商
    substance_strength: str         # 物质强度/用量（从INGREDIENTS AND APPEARANCE提取）
    substance_role: str             # 物质角色（活性成分/非活性成分）
    extraction_source: str          # 信息提取来源
    confidence_score: float         # 信息置信度
    drug_url: str                   # 药品详情页面链接

class UNIILookup:
    """本地UNII数据库查找类"""

    def __init__(self, db_path: str = "unii_database.db"):
        self.db_path = Path(db_path)
        self.translator = ChineseTranslator()
    
    def search_by_name(self, name: str, limit: int = 5) -> List[Dict]:
        """通过名称搜索UNII"""
        if not self.db_path.exists():
            logger.warning("UNII数据库不存在，请先运行: python unii_database.py --download --load")
            return []
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        clean_name = name.strip().lower()
        
        # 精确匹配
        cursor.execute('''
            SELECT unii, substance_name FROM unii_substances 
            WHERE LOWER(substance_name) = ? OR LOWER(display_name) = ?
            LIMIT ?
        ''', (clean_name, clean_name, limit))
        
        results = cursor.fetchall()
        
        # 如果精确匹配没有结果，尝试模糊匹配
        if not results:
            cursor.execute('''
                SELECT unii, substance_name FROM unii_substances 
                WHERE LOWER(substance_name) LIKE ? OR LOWER(display_name) LIKE ?
                ORDER BY 
                    CASE 
                        WHEN LOWER(substance_name) LIKE ? THEN 1
                        WHEN LOWER(display_name) LIKE ? THEN 2
                        ELSE 3
                    END
                LIMIT ?
            ''', (f'%{clean_name}%', f'%{clean_name}%', f'{clean_name}%', f'{clean_name}%', limit))
            
            results = cursor.fetchall()
        
        conn.close()
        
        return [{'unii': row[0], 'substance_name': row[1]} for row in results]
    
    def search_by_unii(self, unii: str) -> Optional[Dict]:
        """通过UNII代码搜索物质信息"""
        if not self.db_path.exists():
            logger.warning("UNII数据库不存在，请先运行: python unii_database.py --download --load")
            return None
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT unii, substance_name FROM unii_substances WHERE unii = ?', 
                      (unii.strip().upper(),))
        result = cursor.fetchone()
        
        conn.close()
        
        if result:
            return {'unii': result[0], 'substance_name': result[1]}
        
        return None
    
    def resolve_substance_info(self, unii_or_name: str) -> Tuple[str, str]:
        """解析物质信息，返回(unii, substance_name)"""
        # 检查是否是UNII代码格式（10个字符，字母数字组合）
        if len(unii_or_name) == 10 and unii_or_name.replace('-', '').isalnum():
            # 可能是UNII代码
            result = self.search_by_unii(unii_or_name)
            if result:
                return result['unii'], result['substance_name']
            else:
                logger.warning(f"未找到UNII代码 {unii_or_name} 对应的物质")
                return unii_or_name, unii_or_name
        else:
            # 可能是物质名称，先处理中文翻译
            search_term = self.translator.process_search_term(unii_or_name)

            # 搜索英文名称
            results = self.search_by_name(search_term, 1)
            if results:
                best_match = results[0]
                return best_match['unii'], best_match['substance_name']
            else:
                # 如果翻译后的英文名称没找到，尝试原始输入
                if search_term != unii_or_name:
                    results = self.search_by_name(unii_or_name, 1)
                    if results:
                        best_match = results[0]
                        return best_match['unii'], best_match['substance_name']

                logger.warning(f"未找到物质名称 {unii_or_name} 对应的UNII代码")
                return "", unii_or_name

class DosageFormExtractor:
    """剂型提取器 - 基于FDA IID标准分类"""

    def __init__(self):
        # FDA IID标准剂型分类词典
        self.dosage_forms = {
            # 口服固体剂型
            'Tablet': ['tablet', 'tab', 'caplet'],
            'Capsule': ['capsule', 'cap'],
            'Powder for Oral Solution': ['powder for oral solution', 'powder for suspension'],
            'Granule': ['granule', 'granules'],

            # 注射剂型
            'Injection': ['injection', 'injectable', 'inj'],
            'Solution for Injection': ['solution for injection', 'injection solution'],
            'Suspension for Injection': ['suspension for injection', 'injection suspension'],
            'Powder for Injection': ['powder for injection', 'lyophilized powder'],

            # 液体剂型
            'Solution': ['solution', 'sol'],
            'Suspension': ['suspension', 'susp'],
            'Syrup': ['syrup', 'elixir'],
            'Drops': ['drops', 'drop'],
            'Emulsion': ['emulsion'],

            # 外用剂型
            'Cream': ['cream', 'creme'],
            'Ointment': ['ointment', 'oint'],
            'Gel': ['gel'],
            'Lotion': ['lotion'],
            'Patch': ['patch', 'transdermal'],
            'Foam': ['foam'],

            # 吸入剂型
            'Inhalation Solution': ['inhalation solution', 'nebulizer solution'],
            'Inhalation Powder': ['inhalation powder', 'dry powder inhaler'],
            'Aerosol': ['aerosol', 'spray', 'metered dose inhaler'],

            # 其他剂型
            'Suppository': ['suppository', 'supp'],
            'Kit': ['kit'],
            'Device': ['device'],
            'Implant': ['implant'],
            'Film': ['film', 'strip'],
            'Lozenge': ['lozenge', 'troche']
        }

        # FDA IID给药途径分类
        self.routes_of_administration = {
            'Oral': ['oral', 'by mouth', 'po'],
            'Intravenous': ['intravenous', 'iv', 'intravenously'],
            'Intramuscular': ['intramuscular', 'im', 'intramuscularly'],
            'Subcutaneous': ['subcutaneous', 'sc', 'sq', 'subq'],
            'Topical': ['topical', 'external', 'cutaneous'],
            'Inhalation': ['inhalation', 'inhaled', 'respiratory'],
            'Ophthalmic': ['ophthalmic', 'eye', 'ocular'],
            'Otic': ['otic', 'ear', 'aural'],
            'Nasal': ['nasal', 'intranasal'],
            'Rectal': ['rectal', 'pr'],
            'Vaginal': ['vaginal', 'intravaginal'],
            'Transdermal': ['transdermal', 'percutaneous']
        }
        
        # 编译正则表达式
        self.dosage_patterns = {}
        for form, keywords in self.dosage_forms.items():
            pattern = r'\b(?:' + '|'.join(re.escape(kw) for kw in keywords) + r')\b'
            self.dosage_patterns[form] = re.compile(pattern, re.IGNORECASE)
    
    def extract_from_name(self, drug_name: str) -> str:
        """从药品名称提取剂型"""
        if not drug_name:
            return ""
        
        # 按优先级匹配剂型
        for form, pattern in self.dosage_patterns.items():
            if pattern.search(drug_name):
                return form.replace('_', ' ').title()
        
        return ""
    
    def extract_from_content(self, content: str) -> str:
        """从页面内容提取剂型"""
        if not content:
            return ""
        
        # 查找剂型相关段落
        dosage_sections = [
            'dosage and administration',
            'description',
            'how supplied',
            'dosage forms and strengths'
        ]
        
        content_lower = content.lower()
        for section in dosage_sections:
            if section in content_lower:
                # 提取该段落内容
                start_idx = content_lower.find(section)
                end_idx = content_lower.find('\n\n', start_idx + 200)  # 查找段落结束
                if end_idx == -1:
                    end_idx = start_idx + 500  # 限制长度
                
                section_content = content[start_idx:end_idx]
                
                # 在段落中查找剂型
                for form, pattern in self.dosage_patterns.items():
                    if pattern.search(section_content):
                        return form.replace('_', ' ').title()
        
        return ""
    
    def determine_dosage_form(self, drug_name: str, content: str) -> Tuple[str, str]:
        """确定剂型，返回(剂型, 来源)"""
        # 首先从药品名称提取
        form_from_name = self.extract_from_name(drug_name)
        if form_from_name:
            return form_from_name, "drug_name"
        
        # 然后从内容提取
        form_from_content = self.extract_from_content(content)
        if form_from_content:
            return form_from_content, "content_analysis"
        
        return "未明确", "unable_to_determine"

    def extract_route_of_administration(self, content: str) -> str:
        """提取给药途径"""
        if not content:
            return ""

        content_lower = content.lower()

        # 优先查找明确的给药途径标记
        route_patterns = [
            (r'route\s+of\s+administration[:\s]+(\w+)', 1),
            (r'administration[:\s]+(\w+)', 1),
            (r'\b(subcutaneous|intravenous|intramuscular|oral|topical|inhalation|ophthalmic|nasal|rectal|vaginal|transdermal)\b', 0)
        ]

        for pattern, group in route_patterns:
            match = re.search(pattern, content_lower)
            if match:
                route_text = match.group(group) if group > 0 else match.group(0)
                # 标准化给药途径
                for route, keywords in self.routes_of_administration.items():
                    if route_text in keywords:
                        return route

        # 如果没有找到明确标记，按关键词匹配
        for route, keywords in self.routes_of_administration.items():
            for keyword in keywords:
                if keyword in content_lower:
                    return route

        return ""

class SubstanceSafetyCrawler:
    """特定物质安全性信息爬虫"""

    def __init__(self, search_term: str, unii_code: str = "", filters: Dict = None):
        self.search_term = search_term
        self.unii_code = unii_code
        self.base_url = "https://dailymed.nlm.nih.gov"
        self.session = None
        self.dosage_extractor = DosageFormExtractor()
        self.substance_data: List[SubstanceInfo] = []
        self.translator = ChineseTranslator()
        self.advanced_filter = AdvancedFilter()
        self.filters = filters or {}
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        connector = aiohttp.TCPConnector(limit=15, limit_per_host=8)
        timeout = aiohttp.ClientTimeout(total=45)
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.session:
            await self.session.close()
    
    async def search_drugs_containing_substance(self, max_pages: int = 10) -> List[str]:
        """搜索包含特定物质的药品"""
        drug_urls = []
        
        # 构建搜索URL
        if self.unii_code:
            search_query = f"({self.unii_code})"
        else:
            search_query = f'"{self.search_term}"'
        
        encoded_query = quote(search_query)
        
        for page in range(1, max_pages + 1):
            search_url = f"{self.base_url}/dailymed/search.cfm?adv=1&labeltype=all&query={encoded_query}&pagesize=20&page={page}"
            
            logger.info(f"获取第 {page} 页的药品链接...")
            
            try:
                async with self.session.get(search_url) as response:
                    if response.status == 200:
                        html = await response.text()
                        soup = BeautifulSoup(html, 'html.parser')
                        
                        # 查找药品链接
                        page_urls = []
                        for link in soup.find_all('a', href=True):
                            href = link.get('href', '')
                            if 'drugInfo.cfm?setid=' in href:
                                full_url = urljoin(self.base_url, href)
                                if full_url not in drug_urls:
                                    drug_urls.append(full_url)
                                    page_urls.append(full_url)
                        
                        logger.info(f"第 {page} 页找到 {len(page_urls)} 个药品链接")
                        
                        # 如果没有找到新的链接，说明已经到最后一页
                        if not page_urls:
                            logger.info(f"第 {page} 页没有找到新的药品链接，停止搜索")
                            break
                    else:
                        logger.warning(f"搜索第 {page} 页失败，状态码: {response.status}")
                        break
                        
            except Exception as e:
                logger.error(f"搜索第 {page} 页时出错: {e}")
                break
            
            # 添加延迟避免请求过快
            await asyncio.sleep(1.5)
        
        logger.info(f"总计找到 {len(drug_urls)} 个药品")
        return drug_urls

    async def extract_substance_info(self, drug_url: str) -> Optional[SubstanceInfo]:
        """从单个药品页面提取特定物质的信息"""
        try:
            async with self.session.get(drug_url) as response:
                if response.status != 200:
                    logger.warning(f"无法访问药品页面: {drug_url}")
                    return None

                html = await response.text()
                soup = BeautifulSoup(html, 'html.parser')

                # 提取基本信息
                drug_info = self._extract_basic_drug_info(soup, drug_url)

                # 提取物质特定信息（专注于INGREDIENTS AND APPEARANCE）
                substance_info = self._extract_from_ingredients_and_appearance(soup)

                # 合并信息
                if drug_info and substance_info:
                    # 生成药品详情页面链接
                    drug_url = f"https://dailymed.nlm.nih.gov/dailymed/drugInfo.cfm?setid={drug_info['drug_id']}"

                    return SubstanceInfo(
                        drug_id=drug_info['drug_id'],
                        ndc_number=drug_info['ndc_number'],
                        drug_name=drug_info['drug_name'],
                        dosage_form=drug_info['dosage_form'],
                        route_of_administration=drug_info['route_of_administration'],
                        manufacturer=drug_info['manufacturer'],
                        substance_strength=substance_info['strength'],
                        substance_role=substance_info['role'],
                        extraction_source=substance_info['source'],
                        confidence_score=substance_info['confidence'],
                        drug_url=drug_url
                    )

                return None

        except Exception as e:
            logger.error(f"处理药品页面时出错 {drug_url}: {e}")
            return None

    def _extract_basic_drug_info(self, soup: BeautifulSoup, url: str) -> Optional[Dict]:
        """提取药品基本信息"""
        try:
            # 提取SetID（药品唯一标识符）
            drug_id = ""
            if 'setid=' in url:
                drug_id = url.split('setid=')[1].split('&')[0]

            # 提取药品名称
            drug_name = ""
            title_elem = soup.find('h1') or soup.find('title')
            if title_elem:
                drug_name = title_elem.get_text(strip=True)

            # 提取剂型和给药途径
            page_text = soup.get_text()
            dosage_form, form_source = self.dosage_extractor.determine_dosage_form(drug_name, page_text)
            route_of_administration = self.dosage_extractor.extract_route_of_administration(page_text)

            # 提取制造商
            manufacturer = ""
            # 查找制造商信息
            for text in soup.find_all(string=True):
                if isinstance(text, str) and ('manufacturer' in text.lower() or 'labeler' in text.lower()):
                    # 提取制造商名称的逻辑
                    parent = text.parent if hasattr(text, 'parent') else None
                    if parent:
                        manufacturer = parent.get_text(strip=True)[:100]
                        break

            # 提取NDC号码
            ndc_number = ""
            ndc_patterns = [
                r'NDC\s*:?\s*(\d{4,5}-\d{3,4}-\d{1,2})',
                r'NDC\s*(\d{11})',
                r'(\d{4,5}-\d{3,4}-\d{1,2})'
            ]

            for pattern in ndc_patterns:
                match = re.search(pattern, page_text, re.IGNORECASE)
                if match:
                    ndc_number = match.group(1)
                    break

            return {
                'drug_id': drug_id,
                'drug_name': drug_name,
                'dosage_form': dosage_form,
                'route_of_administration': route_of_administration,
                'manufacturer': manufacturer,
                'ndc_number': ndc_number
            }

        except Exception as e:
            logger.error(f"提取基本药品信息时出错: {e}")
            return None

    def _extract_from_ingredients_and_appearance(self, soup: BeautifulSoup) -> Optional[Dict]:
        """从INGREDIENTS AND APPEARANCE部分提取特定物质的Strength信息"""
        try:
            # 初始化结果
            result = {
                'strength': '',
                'role': '',
                'source': '',
                'confidence': 0.0
            }

            # 搜索关键词（物质名称和UNII代码）
            search_terms = [self.search_term.upper()]
            if self.unii_code:
                search_terms.append(self.unii_code.upper())

            # 查找所有表格
            tables = soup.find_all('table')

            for table in tables:
                # 查找包含成分信息的表格
                table_text = table.get_text().upper()

                # 检查是否是成分表格
                if any(keyword in table_text for keyword in ['INGREDIENT', 'ACTIVE', 'INACTIVE']):

                    # 查找表格行
                    rows = table.find_all('tr')

                    for row in rows:
                        cells = row.find_all(['td', 'th'])
                        if len(cells) >= 2:

                            # 检查第一列是否包含我们的物质
                            first_cell_text = cells[0].get_text().upper()

                            for term in search_terms:
                                if term in first_cell_text:
                                    # 找到目标物质，提取Strength信息

                                    # 确定物质角色 - 更精确的判断
                                    # 首先检查表格的前面部分是否有明确的标题
                                    table_sections = table.get_text().upper()

                                    # 查找当前行在表格中的位置，判断是在Active还是Inactive部分
                                    all_rows = table.find_all('tr')
                                    current_row_index = -1
                                    for i, r in enumerate(all_rows):
                                        if r == row:
                                            current_row_index = i
                                            break

                                    # 向上查找最近的section标题
                                    role_determined = False
                                    for i in range(current_row_index, -1, -1):
                                        check_row = all_rows[i]
                                        check_text = check_row.get_text().upper()
                                        if 'ACTIVE INGREDIENT' in check_text:
                                            result['role'] = '活性成分'
                                            role_determined = True
                                            break
                                        elif 'INACTIVE INGREDIENT' in check_text:
                                            result['role'] = '非活性成分'
                                            role_determined = True
                                            break

                                    # 如果没有找到明确的section，使用表格整体判断
                                    if not role_determined:
                                        if 'ACTIVE' in table_sections and 'INACTIVE' not in table_sections:
                                            result['role'] = '活性成分'
                                        elif 'INACTIVE' in table_sections and 'ACTIVE' not in table_sections:
                                            result['role'] = '非活性成分'
                                        else:
                                            result['role'] = '成分'

                                    # 查找Strength列
                                    strength_value = ""

                                    # 检查是否有Strength列标题
                                    header_row = table.find('tr')
                                    if header_row:
                                        headers = [th.get_text().upper() for th in header_row.find_all(['th', 'td'])]

                                        # 查找Strength列的索引
                                        strength_col_index = -1
                                        for i, header in enumerate(headers):
                                            if 'STRENGTH' in header:
                                                strength_col_index = i
                                                break

                                        # 如果找到Strength列，提取对应的值
                                        if strength_col_index >= 0 and len(cells) > strength_col_index:
                                            strength_value = cells[strength_col_index].get_text(strip=True)
                                        elif len(cells) >= 2:
                                            # 如果没有明确的Strength列，尝试第二列
                                            strength_value = cells[1].get_text(strip=True)

                                    # 如果Strength值为空或者是表头，尝试其他列
                                    if not strength_value or strength_value.upper() in ['STRENGTH', 'INACTIVE INGREDIENTS']:
                                        for i in range(1, len(cells)):
                                            cell_text = cells[i].get_text(strip=True)
                                            # 检查是否包含数量和单位的模式
                                            if re.search(r'\d+(?:\.\d+)?\s*(mg|g|mcg|μg|ml|l|%)', cell_text, re.IGNORECASE):
                                                strength_value = cell_text
                                                break

                                    result['strength'] = strength_value
                                    result['source'] = 'ingredients_and_appearance'
                                    result['confidence'] = 0.9 if strength_value else 0.5

                                    return result

            # 如果在表格中没有找到，检查页面是否包含该物质
            page_text = soup.get_text().upper()
            for term in search_terms:
                if term in page_text:
                    result['role'] = '页面中提及'
                    result['source'] = 'page_mention'
                    result['confidence'] = 0.3
                    return result

            return None

        except Exception as e:
            logger.error(f"从INGREDIENTS AND APPEARANCE提取信息时出错: {e}")
            return None



    async def process_all_drugs(self, drug_urls: List[str], concurrent: int = 8) -> None:
        """并发处理所有药品"""
        semaphore = asyncio.Semaphore(concurrent)

        async def process_single_drug(url: str) -> Optional[SubstanceInfo]:
            async with semaphore:
                result = await self.extract_substance_info(url)
                if result:
                    logger.info(f"完成: {result.drug_name[:50]}... - {self.search_term}: 找到 (置信度: {result.confidence_score:.1%})")
                else:
                    logger.warning(f"未找到物质信息: {url}")
                await asyncio.sleep(1.5)  # 添加延迟
                return result

        logger.info(f"开始并发处理药品物质信息 (并发数: {concurrent})...")

        tasks = [process_single_drug(url) for url in drug_urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 收集有效结果
        for result in results:
            if isinstance(result, SubstanceInfo):
                self.substance_data.append(result)
            elif isinstance(result, Exception):
                logger.error(f"处理药品时出错: {result}")

        # 应用过滤器
        if self.filters:
            original_count = len(self.substance_data)
            self.substance_data = self.advanced_filter.filter_results(self.substance_data, self.filters)
            filtered_count = len(self.substance_data)
            logger.info(f"应用过滤器后: {original_count} -> {filtered_count} 个记录")

        logger.info(f"完成处理，共收集到 {len(self.substance_data)} 个有效的物质信息记录")

    def save_to_csv(self, output_path: Path) -> None:
        """保存数据到CSV文件"""
        if not self.substance_data:
            logger.warning("没有数据可保存")
            return

        # 准备CSV数据
        csv_data = []
        for item in self.substance_data:
            csv_data.append({
                '检索词（UNII）': self.unii_code if self.unii_code else 'N/A',
                '药品名': item.drug_name,
                '药品ID（标识符）': item.drug_id,
                '用量': item.substance_strength,
                '剂型': item.dosage_form,
                '给药方式': item.route_of_administration,
                '药品链接': item.drug_url,
                'NDC号码': item.ndc_number,
                '制造商': item.manufacturer,
                '物质角色': item.substance_role,
                '信息来源': item.extraction_source,
                '置信度': f"{item.confidence_score:.1%}"
            })

        # 保存到CSV
        df = pd.DataFrame(csv_data)
        df.to_csv(output_path, index=False, encoding='utf-8-sig')
        logger.info(f"CSV文件已保存: {output_path}")

    def save_to_json(self, output_path: Path) -> None:
        """保存详细数据到JSON文件"""
        if not self.substance_data:
            logger.warning("没有数据可保存")
            return

        # 准备JSON数据
        json_data = {
            'search_info': {
                'search_term': self.search_term,
                'unii_code': self.unii_code,
                'total_records': len(self.substance_data),
                'extraction_time': datetime.now().isoformat()
            },
            'substance_data': []
        }

        for item in self.substance_data:
            json_data['substance_data'].append({
                'drug_id': item.drug_id,
                'ndc_number': item.ndc_number,
                'drug_name': item.drug_name,
                'dosage_form': item.dosage_form,
                'route_of_administration': item.route_of_administration,
                'manufacturer': item.manufacturer,
                'substance_strength': item.substance_strength,
                'substance_role': item.substance_role,
                'extraction_source': item.extraction_source,
                'confidence_score': item.confidence_score,
                'drug_url': item.drug_url
            })

        # 保存到JSON
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)

        logger.info(f"JSON文件已保存: {output_path}")

    def generate_statistics(self) -> Dict:
        """生成统计信息"""
        if not self.substance_data:
            return {}

        total_records = len(self.substance_data)

        # 剂型分布
        dosage_forms = {}
        for item in self.substance_data:
            form = item.dosage_form or "未明确"
            dosage_forms[form] = dosage_forms.get(form, 0) + 1

        # 物质角色分布
        roles = {}
        for item in self.substance_data:
            role = item.substance_role or "未明确"
            roles[role] = roles.get(role, 0) + 1

        # 信息来源分布
        sources = {}
        for item in self.substance_data:
            source = item.extraction_source or "未明确"
            sources[source] = sources.get(source, 0) + 1

        # 置信度分布
        high_confidence = sum(1 for item in self.substance_data if item.confidence_score >= 0.7)
        medium_confidence = sum(1 for item in self.substance_data if 0.3 <= item.confidence_score < 0.7)
        low_confidence = sum(1 for item in self.substance_data if item.confidence_score < 0.3)

        # 有Strength信息的记录数
        with_strength = sum(1 for item in self.substance_data if item.substance_strength)

        return {
            'summary': {
                'total_records': total_records,
                'with_strength_info': with_strength,
                'strength_coverage': f"{with_strength/total_records*100:.1f}%" if total_records > 0 else "0%"
            },
            'dosage_forms': dosage_forms,
            'substance_roles': roles,
            'information_sources': sources,
            'confidence_distribution': {
                'high (≥70%)': high_confidence,
                'medium (30-70%)': medium_confidence,
                'low (<30%)': low_confidence
            }
        }

    def print_summary(self) -> None:
        """打印摘要信息"""
        stats = self.generate_statistics()

        print("\n" + "="*80)
        print(f"{self.search_term} 物质安全性信息收集完成！")
        print("="*80)

        if stats:
            summary = stats['summary']
            print(f"数据收集概况:")
            print(f"  总计记录: {summary['total_records']} 个")
            print(f"  包含Strength信息: {summary['with_strength_info']} 个")
            print(f"  Strength信息覆盖率: {summary['strength_coverage']}")

            print(f"\n剂型分布:")
            for form, count in stats['dosage_forms'].items():
                print(f"  {form}: {count} 个")

            print(f"\n物质角色分布:")
            for role, count in stats['substance_roles'].items():
                print(f"  {role}: {count} 个")

            print(f"\n信息来源分布:")
            for source, count in stats['information_sources'].items():
                print(f"  {source}: {count} 个")

            confidence = stats['confidence_distribution']
            print(f"\n置信度分布:")
            print(f"  高置信度 (≥70%): {confidence['high (≥70%)']} 个")
            print(f"  中等置信度 (30-70%): {confidence['medium (30-70%)']} 个")
            print(f"  低置信度 (<30%): {confidence['low (<30%)']} 个")

        print("="*80)

    def print_filter_preview(self) -> None:
        """打印过滤器预览信息"""
        if not self.filters:
            return

        print("\n" + "="*60)
        print("🔍 应用的过滤条件:")
        print("="*60)

        for key, value in self.filters.items():
            if value:
                filter_name = {
                    'dosage_form': '剂型',
                    'route_of_administration': '给药方式',
                    'substance_role': '物质角色',
                    'confidence_range': '置信度范围',
                    'strength_range': '用量范围',
                    'drug_name_pattern': '药品名称模式',
                    'use_regex': '使用正则表达式'
                }.get(key, key)
                print(f"  {filter_name}: {value}")

        # 显示可用的过滤选项
        print(f"\n📋 可用的过滤选项:")
        print(f"  剂型: {', '.join(self.advanced_filter.available_filters['dosage_form'])}")
        print(f"  给药方式: {', '.join(self.advanced_filter.available_filters['route_of_administration'])}")
        print(f"  物质角色: {', '.join(self.advanced_filter.available_filters['substance_role'])}")
        print(f"  置信度范围: high (≥70%), medium (30-70%), low (<30%)")
        print(f"  用量范围: 格式如 '1-100', '>50', '<100', '50'")
        print("="*60)

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='特定物质安全性信息爬虫 - 收集特定物质在药品中的用量、浓度和安全性信息',
        epilog="""
使用示例:
  # 🌏 中文搜索支持
  python substance_safety_crawler.py --substance "海藻糖二水合物"

  # 🔗 智能模式：只提供物质名称，自动查找UNII代码
  python substance_safety_crawler.py --substance "trehalose dihydrate"

  # 🎯 高级过滤功能
  python substance_safety_crawler.py --substance "alcohol" --filter-dosage-form "Injection,Solution" --filter-route "Intravenous"

  # 📊 用量范围过滤
  python substance_safety_crawler.py --substance "trehalose" --filter-strength ">50" --filter-confidence "high"

  # 🔍 药品名称模糊匹配
  python substance_safety_crawler.py --substance "alcohol" --filter-drug-name "FASENRA" --use-regex

  # 📋 显示所有可用过滤选项
  python substance_safety_crawler.py --show-filters

注意: 首次使用前请运行 python unii_database.py --download --load 下载UNII数据库
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )

    # 智能参数 - 可以是UNII代码或物质名称
    parser.add_argument(
        '--substance',
        required=True,
        help='物质名称或UNII代码 (例如: "trehalose dihydrate" 或 "7YIN7J07X4")'
    )

    parser.add_argument(
        '--unii',
        help='物质的UNII代码 (可选，如果未提供将自动查找)'
    )

    # 可选参数
    parser.add_argument(
        '--output',
        default='substance_safety_results',
        help='输出目录 (默认: substance_safety_results)'
    )

    parser.add_argument(
        '--concurrent',
        type=int,
        default=8,
        help='最大并发连接数 (默认: 8)'
    )

    parser.add_argument(
        '--max-pages',
        type=int,
        default=10,
        help='最大搜索页数 (默认: 10)'
    )

    parser.add_argument(
        '--verbose',
        action='store_true',
        help='启用详细日志输出'
    )

    # 高级过滤参数
    parser.add_argument(
        '--filter-dosage-form',
        help='按剂型过滤，多个值用逗号分隔 (如: Injection,Tablet)'
    )

    parser.add_argument(
        '--filter-route',
        help='按给药方式过滤，多个值用逗号分隔 (如: Intravenous,Oral)'
    )

    parser.add_argument(
        '--filter-role',
        help='按物质角色过滤，多个值用逗号分隔 (如: 活性成分,非活性成分)'
    )

    parser.add_argument(
        '--filter-confidence',
        choices=['high', 'medium', 'low'],
        help='按置信度范围过滤 (high: ≥70%%, medium: 30-70%%, low: <30%%)'
    )

    parser.add_argument(
        '--filter-strength',
        help='按用量范围过滤 (格式: "1-100", ">50", "<100", "50")'
    )

    parser.add_argument(
        '--filter-drug-name',
        help='按药品名称模式过滤'
    )

    parser.add_argument(
        '--use-regex',
        action='store_true',
        help='在药品名称过滤中使用正则表达式'
    )

    parser.add_argument(
        '--show-filters',
        action='store_true',
        help='显示可用的过滤选项并退出'
    )

    args = parser.parse_args()

    # 显示过滤选项并退出
    if args.show_filters:
        filter_helper = AdvancedFilter()
        print("📋 可用的过滤选项:")
        print(f"  剂型 (--filter-dosage-form): {', '.join(filter_helper.available_filters['dosage_form'])}")
        print(f"  给药方式 (--filter-route): {', '.join(filter_helper.available_filters['route_of_administration'])}")
        print(f"  物质角色 (--filter-role): {', '.join(filter_helper.available_filters['substance_role'])}")
        print(f"  置信度范围 (--filter-confidence): high (≥70%), medium (30-70%), low (<30%)")
        print(f"  用量范围 (--filter-strength): 格式如 '1-100', '>50', '<100', '50'")
        print(f"  药品名称 (--filter-drug-name): 支持模糊匹配和正则表达式")
        return

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 构建过滤器字典
    filters = {}
    if args.filter_dosage_form:
        filters['dosage_form'] = args.filter_dosage_form
    if args.filter_route:
        filters['route_of_administration'] = args.filter_route
    if args.filter_role:
        filters['substance_role'] = args.filter_role
    if args.filter_confidence:
        filters['confidence_range'] = args.filter_confidence
    if args.filter_strength:
        filters['strength_range'] = args.filter_strength
    if args.filter_drug_name:
        filters['drug_name_pattern'] = args.filter_drug_name
        filters['use_regex'] = args.use_regex

    # 智能解析UNII和物质名称
    unii_lookup = UNIILookup()

    # 如果只提供了substance参数，尝试智能解析
    if args.substance and not args.unii:
        unii_code, substance_name = unii_lookup.resolve_substance_info(args.substance)
        if unii_code:
            args.unii = unii_code
            if substance_name != args.substance:
                logger.info(f"🔍 自动解析: {args.substance} -> UNII: {unii_code}, 标准名称: {substance_name}")
                args.substance = substance_name
        else:
            logger.warning(f"⚠️ 无法解析物质信息: {args.substance}，将使用原始输入")
            args.unii = ""

    # 如果只提供了unii参数，尝试查找物质名称
    elif args.unii and not args.substance:
        result = unii_lookup.search_by_unii(args.unii)
        if result:
            args.substance = result['substance_name']
            logger.info(f"🔍 自动解析: UNII {args.unii} -> {args.substance}")
        else:
            logger.warning(f"⚠️ 无法找到UNII {args.unii} 对应的物质名称")
            args.substance = args.unii

    # 验证必需参数
    if not args.substance:
        logger.error("错误: 必须提供 --substance 参数（可以是物质名称或UNII代码）")
        parser.print_help()
        return

    # 创建输出目录
    output_dir = Path(args.output)
    output_dir.mkdir(exist_ok=True)

    # 生成输出文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    safe_substance_name = re.sub(r'[^\w\-_\.]', '_', args.substance)
    safe_unii = args.unii if args.unii else "NO_UNII"

    csv_file = output_dir / f"{safe_substance_name}_substance_info_{safe_unii}_{timestamp}.csv"
    json_file = output_dir / f"{safe_substance_name}_substance_info_{safe_unii}_{timestamp}.json"
    stats_file = output_dir / f"{safe_substance_name}_substance_info_{safe_unii}_{timestamp}_statistics.json"

    logger.info("启动特定物质安全性信息爬虫...")
    logger.info(f"  检索物质: {args.substance}")
    if args.unii:
        logger.info(f"  UNII代码: {args.unii}")
    logger.info(f"  并发数: {args.concurrent}")
    logger.info(f"  最大搜索页数: {args.max_pages}")
    logger.info(f"  输出目录: {args.output}")

    try:
        # 创建爬虫实例
        async with SubstanceSafetyCrawler(args.substance, args.unii, filters) as crawler:
            # 搜索包含该物质的药品
            drug_urls = await crawler.search_drugs_containing_substance(args.max_pages)

            if not drug_urls:
                logger.error("未找到包含该物质的药品")
                return

            # 处理所有药品
            await crawler.process_all_drugs(drug_urls, args.concurrent)

            if not crawler.substance_data:
                logger.error("未能提取到任何有效的物质信息")
                return

            # 保存结果
            crawler.save_to_csv(csv_file)
            crawler.save_to_json(json_file)

            # 生成并保存统计信息
            stats = crawler.generate_statistics()
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
            logger.info(f"统计文件已保存: {stats_file}")

            # 打印过滤器预览和摘要
            crawler.print_filter_preview()
            crawler.print_summary()

            print(f"\n结果文件:")
            print(f"  物质信息CSV: {csv_file}")
            print(f"  详细JSON数据: {json_file}")
            print(f"  统计分析: {stats_file}")

    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特定物质安全性信息爬虫 - 专注于特定物质在药品中的安全性信息
重新定位：收集特定物质（检索词）在各个药品中的用量、浓度和安全性描述
"""

import asyncio
import aiohttp
import json
import csv
import time
import logging
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Set, Tuple
from urllib.parse import urljoin, quote
from dataclasses import dataclass
import pandas as pd
import re
from bs4 import BeautifulSoup
import sqlite3
from googletrans import Translator
import urllib.parse
import langdetect

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('substance_safety_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

@dataclass
class SubstanceInfo:
    """物质信息数据类"""
    drug_id: str                    # 药品唯一标识符（SetID）
    ndc_number: str                 # NDC号码
    drug_name: str                  # 药品名称
    dosage_form: str                # 剂型
    route_of_administration: str    # 给药途径
    manufacturer: str               # 制造商
    substance_strength: str         # 物质强度/用量（从INGREDIENTS AND APPEARANCE提取）
    substance_role: str             # 物质角色（活性成分/非活性成分）
    extraction_source: str          # 信息提取来源
    confidence_score: float         # 信息置信度
    drug_url: str                   # 药品详情页面链接

class UNIILookup:
    """本地UNII数据库查找类"""
    
    def __init__(self, db_path: str = "unii_database.db"):
        self.db_path = Path(db_path)
    
    def search_by_name(self, name: str, limit: int = 5) -> List[Dict]:
        """通过名称搜索UNII"""
        if not self.db_path.exists():
            logger.warning("UNII数据库不存在，请先运行: python unii_database.py --download --load")
            return []
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        clean_name = name.strip().lower()
        
        # 精确匹配
        cursor.execute('''
            SELECT unii, substance_name FROM unii_substances 
            WHERE LOWER(substance_name) = ? OR LOWER(display_name) = ?
            LIMIT ?
        ''', (clean_name, clean_name, limit))
        
        results = cursor.fetchall()
        
        # 如果精确匹配没有结果，尝试模糊匹配
        if not results:
            cursor.execute('''
                SELECT unii, substance_name FROM unii_substances 
                WHERE LOWER(substance_name) LIKE ? OR LOWER(display_name) LIKE ?
                ORDER BY 
                    CASE 
                        WHEN LOWER(substance_name) LIKE ? THEN 1
                        WHEN LOWER(display_name) LIKE ? THEN 2
                        ELSE 3
                    END
                LIMIT ?
            ''', (f'%{clean_name}%', f'%{clean_name}%', f'{clean_name}%', f'{clean_name}%', limit))
            
            results = cursor.fetchall()
        
        conn.close()
        
        return [{'unii': row[0], 'substance_name': row[1]} for row in results]
    
    def search_by_unii(self, unii: str) -> Optional[Dict]:
        """通过UNII代码搜索物质信息"""
        if not self.db_path.exists():
            logger.warning("UNII数据库不存在，请先运行: python unii_database.py --download --load")
            return None
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT unii, substance_name FROM unii_substances WHERE unii = ?', 
                      (unii.strip().upper(),))
        result = cursor.fetchone()
        
        conn.close()
        
        if result:
            return {'unii': result[0], 'substance_name': result[1]}
        
        return None
    
    def resolve_substance_info(self, unii_or_name: str) -> Tuple[str, str]:
        """解析物质信息，返回(unii, substance_name)"""
        # 检查是否是UNII代码格式（10个字符，字母数字组合）
        if len(unii_or_name) == 10 and unii_or_name.replace('-', '').isalnum():
            # 可能是UNII代码
            result = self.search_by_unii(unii_or_name)
            if result:
                return result['unii'], result['substance_name']
            else:
                logger.warning(f"未找到UNII代码 {unii_or_name} 对应的物质")
                return unii_or_name, unii_or_name
        else:
            # 可能是物质名称
            results = self.search_by_name(unii_or_name, 1)
            if results:
                best_match = results[0]
                return best_match['unii'], best_match['substance_name']
            else:
                logger.warning(f"未找到物质名称 {unii_or_name} 对应的UNII代码")
                return "", unii_or_name

class DosageFormExtractor:
    """剂型提取器 - 基于FDA IID标准分类"""

    def __init__(self):
        # FDA IID标准剂型分类词典
        self.dosage_forms = {
            # 口服固体剂型
            'Tablet': ['tablet', 'tab', 'caplet'],
            'Capsule': ['capsule', 'cap'],
            'Powder for Oral Solution': ['powder for oral solution', 'powder for suspension'],
            'Granule': ['granule', 'granules'],

            # 注射剂型
            'Injection': ['injection', 'injectable', 'inj'],
            'Solution for Injection': ['solution for injection', 'injection solution'],
            'Suspension for Injection': ['suspension for injection', 'injection suspension'],
            'Powder for Injection': ['powder for injection', 'lyophilized powder'],

            # 液体剂型
            'Solution': ['solution', 'sol'],
            'Suspension': ['suspension', 'susp'],
            'Syrup': ['syrup', 'elixir'],
            'Drops': ['drops', 'drop'],
            'Emulsion': ['emulsion'],

            # 外用剂型
            'Cream': ['cream', 'creme'],
            'Ointment': ['ointment', 'oint'],
            'Gel': ['gel'],
            'Lotion': ['lotion'],
            'Patch': ['patch', 'transdermal'],
            'Foam': ['foam'],

            # 吸入剂型
            'Inhalation Solution': ['inhalation solution', 'nebulizer solution'],
            'Inhalation Powder': ['inhalation powder', 'dry powder inhaler'],
            'Aerosol': ['aerosol', 'spray', 'metered dose inhaler'],

            # 其他剂型
            'Suppository': ['suppository', 'supp'],
            'Kit': ['kit'],
            'Device': ['device'],
            'Implant': ['implant'],
            'Film': ['film', 'strip'],
            'Lozenge': ['lozenge', 'troche']
        }

        # FDA IID给药途径分类
        self.routes_of_administration = {
            'Oral': ['oral', 'by mouth', 'po'],
            'Intravenous': ['intravenous', 'iv', 'intravenously'],
            'Intramuscular': ['intramuscular', 'im', 'intramuscularly'],
            'Subcutaneous': ['subcutaneous', 'sc', 'sq', 'subq'],
            'Topical': ['topical', 'external', 'cutaneous'],
            'Inhalation': ['inhalation', 'inhaled', 'respiratory'],
            'Ophthalmic': ['ophthalmic', 'eye', 'ocular'],
            'Otic': ['otic', 'ear', 'aural'],
            'Nasal': ['nasal', 'intranasal'],
            'Rectal': ['rectal', 'pr'],
            'Vaginal': ['vaginal', 'intravaginal'],
            'Transdermal': ['transdermal', 'percutaneous']
        }
        
        # 编译正则表达式
        self.dosage_patterns = {}
        for form, keywords in self.dosage_forms.items():
            pattern = r'\b(?:' + '|'.join(re.escape(kw) for kw in keywords) + r')\b'
            self.dosage_patterns[form] = re.compile(pattern, re.IGNORECASE)
    
    def extract_from_name(self, drug_name: str) -> str:
        """从药品名称提取剂型"""
        if not drug_name:
            return ""
        
        # 按优先级匹配剂型
        for form, pattern in self.dosage_patterns.items():
            if pattern.search(drug_name):
                return form.replace('_', ' ').title()
        
        return ""
    
    def extract_from_content(self, content: str) -> str:
        """从页面内容提取剂型"""
        if not content:
            return ""
        
        # 查找剂型相关段落
        dosage_sections = [
            'dosage and administration',
            'description',
            'how supplied',
            'dosage forms and strengths'
        ]
        
        content_lower = content.lower()
        for section in dosage_sections:
            if section in content_lower:
                # 提取该段落内容
                start_idx = content_lower.find(section)
                end_idx = content_lower.find('\n\n', start_idx + 200)  # 查找段落结束
                if end_idx == -1:
                    end_idx = start_idx + 500  # 限制长度
                
                section_content = content[start_idx:end_idx]
                
                # 在段落中查找剂型
                for form, pattern in self.dosage_patterns.items():
                    if pattern.search(section_content):
                        return form.replace('_', ' ').title()
        
        return ""
    
    def determine_dosage_form(self, drug_name: str, content: str) -> Tuple[str, str]:
        """确定剂型，返回(剂型, 来源)"""
        # 首先从药品名称提取
        form_from_name = self.extract_from_name(drug_name)
        if form_from_name:
            return form_from_name, "drug_name"
        
        # 然后从内容提取
        form_from_content = self.extract_from_content(content)
        if form_from_content:
            return form_from_content, "content_analysis"
        
        return "未明确", "unable_to_determine"

    def extract_route_of_administration(self, content: str) -> str:
        """提取给药途径"""
        if not content:
            return ""

        content_lower = content.lower()

        # 优先查找明确的给药途径标记
        route_patterns = [
            (r'route\s+of\s+administration[:\s]+(\w+)', 1),
            (r'administration[:\s]+(\w+)', 1),
            (r'\b(subcutaneous|intravenous|intramuscular|oral|topical|inhalation|ophthalmic|nasal|rectal|vaginal|transdermal)\b', 0)
        ]

        for pattern, group in route_patterns:
            match = re.search(pattern, content_lower)
            if match:
                route_text = match.group(group) if group > 0 else match.group(0)
                # 标准化给药途径
                for route, keywords in self.routes_of_administration.items():
                    if route_text in keywords:
                        return route

        # 如果没有找到明确标记，按关键词匹配
        for route, keywords in self.routes_of_administration.items():
            for keyword in keywords:
                if keyword in content_lower:
                    return route

        return ""

class SubstanceSafetyCrawler:
    """特定物质安全性信息爬虫"""
    
    def __init__(self, search_term: str, unii_code: str = ""):
        self.search_term = search_term
        self.unii_code = unii_code
        self.base_url = "https://dailymed.nlm.nih.gov"
        self.session = None
        self.dosage_extractor = DosageFormExtractor()
        self.substance_data: List[SubstanceInfo] = []
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        connector = aiohttp.TCPConnector(limit=15, limit_per_host=8)
        timeout = aiohttp.ClientTimeout(total=45)
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.session:
            await self.session.close()
    
    async def search_drugs_containing_substance(self, max_pages: int = 10) -> List[str]:
        """搜索包含特定物质的药品"""
        drug_urls = []
        
        # 构建搜索URL
        if self.unii_code:
            search_query = f"({self.unii_code})"
        else:
            search_query = f'"{self.search_term}"'
        
        encoded_query = quote(search_query)
        
        for page in range(1, max_pages + 1):
            search_url = f"{self.base_url}/dailymed/search.cfm?adv=1&labeltype=all&query={encoded_query}&pagesize=20&page={page}"
            
            logger.info(f"获取第 {page} 页的药品链接...")
            
            try:
                async with self.session.get(search_url) as response:
                    if response.status == 200:
                        html = await response.text()
                        soup = BeautifulSoup(html, 'html.parser')
                        
                        # 查找药品链接
                        page_urls = []
                        for link in soup.find_all('a', href=True):
                            href = link.get('href', '')
                            if 'drugInfo.cfm?setid=' in href:
                                full_url = urljoin(self.base_url, href)
                                if full_url not in drug_urls:
                                    drug_urls.append(full_url)
                                    page_urls.append(full_url)
                        
                        logger.info(f"第 {page} 页找到 {len(page_urls)} 个药品链接")
                        
                        # 如果没有找到新的链接，说明已经到最后一页
                        if not page_urls:
                            logger.info(f"第 {page} 页没有找到新的药品链接，停止搜索")
                            break
                    else:
                        logger.warning(f"搜索第 {page} 页失败，状态码: {response.status}")
                        break
                        
            except Exception as e:
                logger.error(f"搜索第 {page} 页时出错: {e}")
                break
            
            # 添加延迟避免请求过快
            await asyncio.sleep(1.5)
        
        logger.info(f"总计找到 {len(drug_urls)} 个药品")
        return drug_urls

    async def extract_substance_info(self, drug_url: str) -> Optional[SubstanceInfo]:
        """从单个药品页面提取特定物质的信息"""
        try:
            async with self.session.get(drug_url) as response:
                if response.status != 200:
                    logger.warning(f"无法访问药品页面: {drug_url}")
                    return None

                html = await response.text()
                soup = BeautifulSoup(html, 'html.parser')

                # 提取基本信息
                drug_info = self._extract_basic_drug_info(soup, drug_url)

                # 提取物质特定信息（专注于INGREDIENTS AND APPEARANCE）
                substance_info = self._extract_from_ingredients_and_appearance(soup)

                # 合并信息
                if drug_info and substance_info:
                    return SubstanceInfo(
                        drug_id=drug_info['drug_id'],
                        ndc_number=drug_info['ndc_number'],
                        drug_name=drug_info['drug_name'],
                        dosage_form=drug_info['dosage_form'],
                        route_of_administration=drug_info['route_of_administration'],
                        manufacturer=drug_info['manufacturer'],
                        substance_strength=substance_info['strength'],
                        substance_role=substance_info['role'],
                        extraction_source=substance_info['source'],
                        confidence_score=substance_info['confidence']
                    )

                return None

        except Exception as e:
            logger.error(f"处理药品页面时出错 {drug_url}: {e}")
            return None

    def _extract_basic_drug_info(self, soup: BeautifulSoup, url: str) -> Optional[Dict]:
        """提取药品基本信息"""
        try:
            # 提取SetID（药品唯一标识符）
            drug_id = ""
            if 'setid=' in url:
                drug_id = url.split('setid=')[1].split('&')[0]

            # 提取药品名称
            drug_name = ""
            title_elem = soup.find('h1') or soup.find('title')
            if title_elem:
                drug_name = title_elem.get_text(strip=True)

            # 提取剂型和给药途径
            page_text = soup.get_text()
            dosage_form, form_source = self.dosage_extractor.determine_dosage_form(drug_name, page_text)
            route_of_administration = self.dosage_extractor.extract_route_of_administration(page_text)

            # 提取制造商
            manufacturer = ""
            # 查找制造商信息
            for text in soup.find_all(string=True):
                if isinstance(text, str) and ('manufacturer' in text.lower() or 'labeler' in text.lower()):
                    # 提取制造商名称的逻辑
                    parent = text.parent if hasattr(text, 'parent') else None
                    if parent:
                        manufacturer = parent.get_text(strip=True)[:100]
                        break

            # 提取NDC号码
            ndc_number = ""
            ndc_patterns = [
                r'NDC\s*:?\s*(\d{4,5}-\d{3,4}-\d{1,2})',
                r'NDC\s*(\d{11})',
                r'(\d{4,5}-\d{3,4}-\d{1,2})'
            ]

            for pattern in ndc_patterns:
                match = re.search(pattern, page_text, re.IGNORECASE)
                if match:
                    ndc_number = match.group(1)
                    break

            return {
                'drug_id': drug_id,
                'drug_name': drug_name,
                'dosage_form': dosage_form,
                'route_of_administration': route_of_administration,
                'manufacturer': manufacturer,
                'ndc_number': ndc_number
            }

        except Exception as e:
            logger.error(f"提取基本药品信息时出错: {e}")
            return None

    def _extract_from_ingredients_and_appearance(self, soup: BeautifulSoup) -> Optional[Dict]:
        """从INGREDIENTS AND APPEARANCE部分提取特定物质的Strength信息"""
        try:
            # 初始化结果
            result = {
                'strength': '',
                'role': '',
                'source': '',
                'confidence': 0.0
            }

            # 搜索关键词（物质名称和UNII代码）
            search_terms = [self.search_term.upper()]
            if self.unii_code:
                search_terms.append(self.unii_code.upper())

            # 查找所有表格
            tables = soup.find_all('table')

            for table in tables:
                # 查找包含成分信息的表格
                table_text = table.get_text().upper()

                # 检查是否是成分表格
                if any(keyword in table_text for keyword in ['INGREDIENT', 'ACTIVE', 'INACTIVE']):

                    # 查找表格行
                    rows = table.find_all('tr')

                    for row in rows:
                        cells = row.find_all(['td', 'th'])
                        if len(cells) >= 2:

                            # 检查第一列是否包含我们的物质
                            first_cell_text = cells[0].get_text().upper()

                            for term in search_terms:
                                if term in first_cell_text:
                                    # 找到目标物质，提取Strength信息

                                    # 确定物质角色 - 更精确的判断
                                    # 首先检查表格的前面部分是否有明确的标题
                                    table_sections = table.get_text().upper()

                                    # 查找当前行在表格中的位置，判断是在Active还是Inactive部分
                                    all_rows = table.find_all('tr')
                                    current_row_index = -1
                                    for i, r in enumerate(all_rows):
                                        if r == row:
                                            current_row_index = i
                                            break

                                    # 向上查找最近的section标题
                                    role_determined = False
                                    for i in range(current_row_index, -1, -1):
                                        check_row = all_rows[i]
                                        check_text = check_row.get_text().upper()
                                        if 'ACTIVE INGREDIENT' in check_text:
                                            result['role'] = '活性成分'
                                            role_determined = True
                                            break
                                        elif 'INACTIVE INGREDIENT' in check_text:
                                            result['role'] = '非活性成分'
                                            role_determined = True
                                            break

                                    # 如果没有找到明确的section，使用表格整体判断
                                    if not role_determined:
                                        if 'ACTIVE' in table_sections and 'INACTIVE' not in table_sections:
                                            result['role'] = '活性成分'
                                        elif 'INACTIVE' in table_sections and 'ACTIVE' not in table_sections:
                                            result['role'] = '非活性成分'
                                        else:
                                            result['role'] = '成分'

                                    # 查找Strength列
                                    strength_value = ""

                                    # 检查是否有Strength列标题
                                    header_row = table.find('tr')
                                    if header_row:
                                        headers = [th.get_text().upper() for th in header_row.find_all(['th', 'td'])]

                                        # 查找Strength列的索引
                                        strength_col_index = -1
                                        for i, header in enumerate(headers):
                                            if 'STRENGTH' in header:
                                                strength_col_index = i
                                                break

                                        # 如果找到Strength列，提取对应的值
                                        if strength_col_index >= 0 and len(cells) > strength_col_index:
                                            strength_value = cells[strength_col_index].get_text(strip=True)
                                        elif len(cells) >= 2:
                                            # 如果没有明确的Strength列，尝试第二列
                                            strength_value = cells[1].get_text(strip=True)

                                    # 如果Strength值为空或者是表头，尝试其他列
                                    if not strength_value or strength_value.upper() in ['STRENGTH', 'INACTIVE INGREDIENTS']:
                                        for i in range(1, len(cells)):
                                            cell_text = cells[i].get_text(strip=True)
                                            # 检查是否包含数量和单位的模式
                                            if re.search(r'\d+(?:\.\d+)?\s*(mg|g|mcg|μg|ml|l|%)', cell_text, re.IGNORECASE):
                                                strength_value = cell_text
                                                break

                                    result['strength'] = strength_value
                                    result['source'] = 'ingredients_and_appearance'
                                    result['confidence'] = 0.9 if strength_value else 0.5

                                    return result

            # 如果在表格中没有找到，检查页面是否包含该物质
            page_text = soup.get_text().upper()
            for term in search_terms:
                if term in page_text:
                    result['role'] = '页面中提及'
                    result['source'] = 'page_mention'
                    result['confidence'] = 0.3
                    return result

            return None

        except Exception as e:
            logger.error(f"从INGREDIENTS AND APPEARANCE提取信息时出错: {e}")
            return None



    async def process_all_drugs(self, drug_urls: List[str], concurrent: int = 8) -> None:
        """并发处理所有药品"""
        semaphore = asyncio.Semaphore(concurrent)

        async def process_single_drug(url: str) -> Optional[SubstanceInfo]:
            async with semaphore:
                result = await self.extract_substance_info(url)
                if result:
                    logger.info(f"完成: {result.drug_name[:50]}... - {self.search_term}: 找到 (置信度: {result.confidence_score:.1%})")
                else:
                    logger.warning(f"未找到物质信息: {url}")
                await asyncio.sleep(1.5)  # 添加延迟
                return result

        logger.info(f"开始并发处理药品物质信息 (并发数: {concurrent})...")

        tasks = [process_single_drug(url) for url in drug_urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 收集有效结果
        for result in results:
            if isinstance(result, SubstanceInfo):
                self.substance_data.append(result)
            elif isinstance(result, Exception):
                logger.error(f"处理药品时出错: {result}")

        logger.info(f"完成处理，共收集到 {len(self.substance_data)} 个有效的物质信息记录")

    def save_to_csv(self, output_path: Path) -> None:
        """保存数据到CSV文件"""
        if not self.substance_data:
            logger.warning("没有数据可保存")
            return

        # 准备CSV数据
        csv_data = []
        for item in self.substance_data:
            csv_data.append({
                '检索词（UNII）': self.unii_code if self.unii_code else 'N/A',
                '药品名': item.drug_name,
                '药品ID（标识符）': item.drug_id,
                '用量': item.substance_strength,
                '剂型': item.dosage_form,
                '给药方式': item.route_of_administration,
                'NDC号码': item.ndc_number,
                '制造商': item.manufacturer,
                '物质角色': item.substance_role,
                '信息来源': item.extraction_source,
                '置信度': f"{item.confidence_score:.1%}"
            })

        # 保存到CSV
        df = pd.DataFrame(csv_data)
        df.to_csv(output_path, index=False, encoding='utf-8-sig')
        logger.info(f"CSV文件已保存: {output_path}")

    def save_to_json(self, output_path: Path) -> None:
        """保存详细数据到JSON文件"""
        if not self.substance_data:
            logger.warning("没有数据可保存")
            return

        # 准备JSON数据
        json_data = {
            'search_info': {
                'search_term': self.search_term,
                'unii_code': self.unii_code,
                'total_records': len(self.substance_data),
                'extraction_time': datetime.now().isoformat()
            },
            'substance_data': []
        }

        for item in self.substance_data:
            json_data['substance_data'].append({
                'drug_id': item.drug_id,
                'ndc_number': item.ndc_number,
                'drug_name': item.drug_name,
                'dosage_form': item.dosage_form,
                'route_of_administration': item.route_of_administration,
                'manufacturer': item.manufacturer,
                'substance_strength': item.substance_strength,
                'substance_role': item.substance_role,
                'extraction_source': item.extraction_source,
                'confidence_score': item.confidence_score
            })

        # 保存到JSON
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)

        logger.info(f"JSON文件已保存: {output_path}")

    def generate_statistics(self) -> Dict:
        """生成统计信息"""
        if not self.substance_data:
            return {}

        total_records = len(self.substance_data)

        # 剂型分布
        dosage_forms = {}
        for item in self.substance_data:
            form = item.dosage_form or "未明确"
            dosage_forms[form] = dosage_forms.get(form, 0) + 1

        # 物质角色分布
        roles = {}
        for item in self.substance_data:
            role = item.substance_role or "未明确"
            roles[role] = roles.get(role, 0) + 1

        # 信息来源分布
        sources = {}
        for item in self.substance_data:
            source = item.extraction_source or "未明确"
            sources[source] = sources.get(source, 0) + 1

        # 置信度分布
        high_confidence = sum(1 for item in self.substance_data if item.confidence_score >= 0.7)
        medium_confidence = sum(1 for item in self.substance_data if 0.3 <= item.confidence_score < 0.7)
        low_confidence = sum(1 for item in self.substance_data if item.confidence_score < 0.3)

        # 有Strength信息的记录数
        with_strength = sum(1 for item in self.substance_data if item.substance_strength)

        return {
            'summary': {
                'total_records': total_records,
                'with_strength_info': with_strength,
                'strength_coverage': f"{with_strength/total_records*100:.1f}%" if total_records > 0 else "0%"
            },
            'dosage_forms': dosage_forms,
            'substance_roles': roles,
            'information_sources': sources,
            'confidence_distribution': {
                'high (≥70%)': high_confidence,
                'medium (30-70%)': medium_confidence,
                'low (<30%)': low_confidence
            }
        }

    def print_summary(self) -> None:
        """打印摘要信息"""
        stats = self.generate_statistics()

        print("\n" + "="*80)
        print(f"{self.search_term} 物质安全性信息收集完成！")
        print("="*80)

        if stats:
            summary = stats['summary']
            print(f"数据收集概况:")
            print(f"  总计记录: {summary['total_records']} 个")
            print(f"  包含Strength信息: {summary['with_strength_info']} 个")
            print(f"  Strength信息覆盖率: {summary['strength_coverage']}")

            print(f"\n剂型分布:")
            for form, count in stats['dosage_forms'].items():
                print(f"  {form}: {count} 个")

            print(f"\n物质角色分布:")
            for role, count in stats['substance_roles'].items():
                print(f"  {role}: {count} 个")

            print(f"\n信息来源分布:")
            for source, count in stats['information_sources'].items():
                print(f"  {source}: {count} 个")

            confidence = stats['confidence_distribution']
            print(f"\n置信度分布:")
            print(f"  高置信度 (≥70%): {confidence['high (≥70%)']} 个")
            print(f"  中等置信度 (30-70%): {confidence['medium (30-70%)']} 个")
            print(f"  低置信度 (<30%): {confidence['low (<30%)']} 个")

        print("="*80)

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='特定物质安全性信息爬虫 - 收集特定物质在药品中的用量、浓度和安全性信息',
        epilog="""
使用示例:
  # 智能模式：只提供物质名称，自动查找UNII代码
  python substance_safety_crawler.py --substance "trehalose dihydrate"

  # 智能模式：只提供UNII代码，自动查找物质名称
  python substance_safety_crawler.py --substance "7YIN7J07X4"

  # 传统模式：同时提供UNII和物质名称
  python substance_safety_crawler.py --substance "trehalose dihydrate" --unii "7YIN7J07X4"

  # 自定义参数
  python substance_safety_crawler.py --substance "alcohol" --concurrent 6 --max-pages 5 --output "alcohol_substance_info"

注意: 首次使用前请运行 python unii_database.py --download --load 下载UNII数据库
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )

    # 智能参数 - 可以是UNII代码或物质名称
    parser.add_argument(
        '--substance',
        required=True,
        help='物质名称或UNII代码 (例如: "trehalose dihydrate" 或 "7YIN7J07X4")'
    )

    parser.add_argument(
        '--unii',
        help='物质的UNII代码 (可选，如果未提供将自动查找)'
    )

    # 可选参数
    parser.add_argument(
        '--output',
        default='substance_safety_results',
        help='输出目录 (默认: substance_safety_results)'
    )

    parser.add_argument(
        '--concurrent',
        type=int,
        default=8,
        help='最大并发连接数 (默认: 8)'
    )

    parser.add_argument(
        '--max-pages',
        type=int,
        default=10,
        help='最大搜索页数 (默认: 10)'
    )

    parser.add_argument(
        '--verbose',
        action='store_true',
        help='启用详细日志输出'
    )

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 智能解析UNII和物质名称
    unii_lookup = UNIILookup()

    # 如果只提供了substance参数，尝试智能解析
    if args.substance and not args.unii:
        unii_code, substance_name = unii_lookup.resolve_substance_info(args.substance)
        if unii_code:
            args.unii = unii_code
            if substance_name != args.substance:
                logger.info(f"🔍 自动解析: {args.substance} -> UNII: {unii_code}, 标准名称: {substance_name}")
                args.substance = substance_name
        else:
            logger.warning(f"⚠️ 无法解析物质信息: {args.substance}，将使用原始输入")
            args.unii = ""

    # 如果只提供了unii参数，尝试查找物质名称
    elif args.unii and not args.substance:
        result = unii_lookup.search_by_unii(args.unii)
        if result:
            args.substance = result['substance_name']
            logger.info(f"🔍 自动解析: UNII {args.unii} -> {args.substance}")
        else:
            logger.warning(f"⚠️ 无法找到UNII {args.unii} 对应的物质名称")
            args.substance = args.unii

    # 验证必需参数
    if not args.substance:
        logger.error("错误: 必须提供 --substance 参数（可以是物质名称或UNII代码）")
        parser.print_help()
        return

    # 创建输出目录
    output_dir = Path(args.output)
    output_dir.mkdir(exist_ok=True)

    # 生成输出文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    safe_substance_name = re.sub(r'[^\w\-_\.]', '_', args.substance)
    safe_unii = args.unii if args.unii else "NO_UNII"

    csv_file = output_dir / f"{safe_substance_name}_substance_info_{safe_unii}_{timestamp}.csv"
    json_file = output_dir / f"{safe_substance_name}_substance_info_{safe_unii}_{timestamp}.json"
    stats_file = output_dir / f"{safe_substance_name}_substance_info_{safe_unii}_{timestamp}_statistics.json"

    logger.info("启动特定物质安全性信息爬虫...")
    logger.info(f"  检索物质: {args.substance}")
    if args.unii:
        logger.info(f"  UNII代码: {args.unii}")
    logger.info(f"  并发数: {args.concurrent}")
    logger.info(f"  最大搜索页数: {args.max_pages}")
    logger.info(f"  输出目录: {args.output}")

    try:
        # 创建爬虫实例
        async with SubstanceSafetyCrawler(args.substance, args.unii) as crawler:
            # 搜索包含该物质的药品
            drug_urls = await crawler.search_drugs_containing_substance(args.max_pages)

            if not drug_urls:
                logger.error("未找到包含该物质的药品")
                return

            # 处理所有药品
            await crawler.process_all_drugs(drug_urls, args.concurrent)

            if not crawler.substance_data:
                logger.error("未能提取到任何有效的物质信息")
                return

            # 保存结果
            crawler.save_to_csv(csv_file)
            crawler.save_to_json(json_file)

            # 生成并保存统计信息
            stats = crawler.generate_statistics()
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
            logger.info(f"统计文件已保存: {stats_file}")

            # 打印摘要
            crawler.print_summary()

            print(f"\n结果文件:")
            print(f"  物质信息CSV: {csv_file}")
            print(f"  详细JSON数据: {json_file}")
            print(f"  统计分析: {stats_file}")

    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())

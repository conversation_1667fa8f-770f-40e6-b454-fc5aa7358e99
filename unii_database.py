#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地UNII数据库管理工具
从 https://precision.fda.gov/uniisearch/archive 下载并管理UNII数据
"""

import asyncio
import aiohttp
import pandas as pd
import sqlite3
import json
import re
import zipfile
import io
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from urllib.parse import urljoin
import argparse
from datetime import datetime

class UNIIDatabase:
    """本地UNII数据库管理类"""
    
    def __init__(self, db_path: str = "unii_database.db"):
        self.db_path = Path(db_path)
        self.download_url = "https://precision.fda.gov/uniisearch/archive"
        self.data_files = []
        
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建UNII表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS unii_substances (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                unii TEXT UNIQUE NOT NULL,
                substance_name TEXT NOT NULL,
                display_name TEXT,
                substance_class TEXT,
                cas_number TEXT,
                molecular_formula TEXT,
                molecular_weight TEXT,
                inchi TEXT,
                inchi_key TEXT,
                smiles TEXT,
                status TEXT,
                created_date TEXT,
                modified_date TEXT,
                synonyms TEXT,
                UNIQUE(unii)
            )
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_unii ON unii_substances(unii)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_name ON unii_substances(substance_name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_display_name ON unii_substances(display_name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_cas ON unii_substances(cas_number)')
        
        # 创建元数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS database_metadata (
                key TEXT PRIMARY KEY,
                value TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
        
        print(f"✅ 数据库初始化完成: {self.db_path}")
    
    async def download_latest_data(self):
        """下载最新的UNII数据"""
        print("🔍 正在获取最新的UNII数据文件列表...")
        
        async with aiohttp.ClientSession() as session:
            try:
                # 获取下载页面
                async with session.get(self.download_url) as response:
                    if response.status == 200:
                        html = await response.text()
                        
                        # 查找下载链接 (通常是.zip或.txt文件)
                        import re
                        zip_links = re.findall(r'href="([^"]*\.zip)"', html)
                        txt_links = re.findall(r'href="([^"]*\.txt)"', html)
                        
                        if zip_links:
                            # 选择最新的zip文件
                            latest_zip = zip_links[0]
                            download_url = urljoin(self.download_url, latest_zip)
                            print(f"📥 下载文件: {download_url}")
                            
                            await self._download_and_extract(session, download_url)
                        elif txt_links:
                            # 如果没有zip文件，尝试txt文件
                            latest_txt = txt_links[0]
                            download_url = urljoin(self.download_url, latest_txt)
                            print(f"📥 下载文件: {download_url}")
                            
                            await self._download_txt_file(session, download_url)
                        else:
                            print("❌ 未找到可下载的UNII数据文件")
                            return False
                            
                    else:
                        print(f"❌ 无法访问下载页面，状态码: {response.status}")
                        return False
                        
            except Exception as e:
                print(f"❌ 下载失败: {e}")
                return False
        
        return True
    
    async def _download_and_extract(self, session: aiohttp.ClientSession, url: str):
        """下载并解压zip文件"""
        async with session.get(url) as response:
            if response.status == 200:
                zip_data = await response.read()
                
                # 解压zip文件
                with zipfile.ZipFile(io.BytesIO(zip_data)) as zip_file:
                    for file_name in zip_file.namelist():
                        if file_name.endswith('.txt') or file_name.endswith('.csv'):
                            print(f"📄 解压文件: {file_name}")
                            
                            with zip_file.open(file_name) as file:
                                content = file.read().decode('utf-8', errors='ignore')
                                
                            # 保存到本地
                            local_file = Path(file_name)
                            with open(local_file, 'w', encoding='utf-8') as f:
                                f.write(content)
                            
                            self.data_files.append(local_file)
                            
                print(f"✅ 下载并解压完成，共 {len(self.data_files)} 个文件")
            else:
                print(f"❌ 下载失败，状态码: {response.status}")
    
    async def _download_txt_file(self, session: aiohttp.ClientSession, url: str):
        """下载txt文件"""
        async with session.get(url) as response:
            if response.status == 200:
                content = await response.text()
                
                # 保存到本地
                file_name = Path(url).name
                local_file = Path(file_name)
                
                with open(local_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.data_files.append(local_file)
                print(f"✅ 下载完成: {file_name}")
            else:
                print(f"❌ 下载失败，状态码: {response.status}")
    
    def load_data_to_database(self):
        """将下载的数据加载到数据库"""
        if not self.data_files:
            print("❌ 没有找到数据文件，请先下载数据")
            return False
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        total_records = 0
        
        for data_file in self.data_files:
            print(f"📊 处理文件: {data_file}")
            
            try:
                # 尝试读取为CSV
                if data_file.suffix.lower() == '.csv':
                    df = pd.read_csv(data_file, encoding='utf-8', low_memory=False)
                else:
                    # 尝试读取为制表符分隔的文件
                    df = pd.read_csv(data_file, sep='\t', encoding='utf-8', low_memory=False)
                
                # 标准化列名
                df.columns = df.columns.str.lower().str.replace(' ', '_')
                
                # 处理每一行数据
                for _, row in df.iterrows():
                    try:
                        # 提取关键字段
                        unii = str(row.get('unii', row.get('approval_id', ''))).strip()
                        substance_name = str(row.get('substance_name', row.get('name', ''))).strip()
                        
                        if not unii or not substance_name or unii == 'nan' or substance_name == 'nan':
                            continue
                        
                        # 准备数据
                        data = {
                            'unii': unii,
                            'substance_name': substance_name,
                            'display_name': str(row.get('display_name', substance_name)).strip(),
                            'substance_class': str(row.get('substance_class', '')).strip(),
                            'cas_number': str(row.get('cas_number', row.get('cas', ''))).strip(),
                            'molecular_formula': str(row.get('molecular_formula', '')).strip(),
                            'molecular_weight': str(row.get('molecular_weight', '')).strip(),
                            'inchi': str(row.get('inchi', '')).strip(),
                            'inchi_key': str(row.get('inchi_key', '')).strip(),
                            'smiles': str(row.get('smiles', '')).strip(),
                            'status': str(row.get('status', 'Active')).strip(),
                            'created_date': str(row.get('created_date', '')).strip(),
                            'modified_date': str(row.get('modified_date', '')).strip(),
                            'synonyms': ''  # 同义词需要特殊处理
                        }
                        
                        # 清理空值
                        for key, value in data.items():
                            if value in ['nan', 'None', '']:
                                data[key] = ''
                        
                        # 插入数据库
                        cursor.execute('''
                            INSERT OR REPLACE INTO unii_substances 
                            (unii, substance_name, display_name, substance_class, cas_number,
                             molecular_formula, molecular_weight, inchi, inchi_key, smiles,
                             status, created_date, modified_date, synonyms)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            data['unii'], data['substance_name'], data['display_name'],
                            data['substance_class'], data['cas_number'], data['molecular_formula'],
                            data['molecular_weight'], data['inchi'], data['inchi_key'],
                            data['smiles'], data['status'], data['created_date'],
                            data['modified_date'], data['synonyms']
                        ))
                        
                        total_records += 1
                        
                        if total_records % 1000 == 0:
                            print(f"  已处理 {total_records} 条记录...")
                            conn.commit()
                            
                    except Exception as e:
                        print(f"  ⚠️ 处理记录时出错: {e}")
                        continue
                
                print(f"✅ 文件 {data_file} 处理完成")
                
            except Exception as e:
                print(f"❌ 处理文件 {data_file} 失败: {e}")
                continue
        
        # 更新元数据
        cursor.execute('''
            INSERT OR REPLACE INTO database_metadata (key, value)
            VALUES ('last_update', ?)
        ''', (datetime.now().isoformat(),))
        
        cursor.execute('''
            INSERT OR REPLACE INTO database_metadata (key, value)
            VALUES ('total_records', ?)
        ''', (str(total_records),))
        
        conn.commit()
        conn.close()
        
        print(f"🎉 数据库更新完成！总计 {total_records} 条记录")
        return True
    
    def search_by_name(self, name: str, limit: int = 10) -> List[Dict]:
        """通过名称搜索UNII"""
        if not self.db_path.exists():
            print("❌ 数据库不存在，请先下载并初始化数据")
            return []
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 清理搜索词
        clean_name = name.strip().lower()
        
        # 精确匹配
        cursor.execute('''
            SELECT * FROM unii_substances 
            WHERE LOWER(substance_name) = ? OR LOWER(display_name) = ?
            LIMIT ?
        ''', (clean_name, clean_name, limit))
        
        results = cursor.fetchall()
        
        # 如果精确匹配没有结果，尝试模糊匹配
        if not results:
            cursor.execute('''
                SELECT * FROM unii_substances 
                WHERE LOWER(substance_name) LIKE ? OR LOWER(display_name) LIKE ?
                ORDER BY 
                    CASE 
                        WHEN LOWER(substance_name) LIKE ? THEN 1
                        WHEN LOWER(display_name) LIKE ? THEN 2
                        ELSE 3
                    END
                LIMIT ?
            ''', (f'%{clean_name}%', f'%{clean_name}%', f'{clean_name}%', f'{clean_name}%', limit))
            
            results = cursor.fetchall()
        
        conn.close()
        
        # 转换为字典格式
        columns = ['id', 'unii', 'substance_name', 'display_name', 'substance_class',
                  'cas_number', 'molecular_formula', 'molecular_weight', 'inchi',
                  'inchi_key', 'smiles', 'status', 'created_date', 'modified_date', 'synonyms']
        
        return [dict(zip(columns, row)) for row in results]
    
    def search_by_unii(self, unii: str) -> Optional[Dict]:
        """通过UNII代码搜索物质信息"""
        if not self.db_path.exists():
            print("❌ 数据库不存在，请先下载并初始化数据")
            return None
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM unii_substances WHERE unii = ?', (unii.strip().upper(),))
        result = cursor.fetchone()
        
        conn.close()
        
        if result:
            columns = ['id', 'unii', 'substance_name', 'display_name', 'substance_class',
                      'cas_number', 'molecular_formula', 'molecular_weight', 'inchi',
                      'inchi_key', 'smiles', 'status', 'created_date', 'modified_date', 'synonyms']
            return dict(zip(columns, result))
        
        return None
    
    def get_database_info(self) -> Dict:
        """获取数据库信息"""
        if not self.db_path.exists():
            return {'status': 'not_exists'}
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 获取记录总数
        cursor.execute('SELECT COUNT(*) FROM unii_substances')
        total_records = cursor.fetchone()[0]
        
        # 获取元数据
        cursor.execute('SELECT key, value FROM database_metadata')
        metadata = dict(cursor.fetchall())
        
        conn.close()
        
        return {
            'status': 'exists',
            'total_records': total_records,
            'last_update': metadata.get('last_update', 'Unknown'),
            'database_size': f"{self.db_path.stat().st_size / 1024 / 1024:.1f} MB"
        }

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='本地UNII数据库管理工具')
    parser.add_argument('--download', action='store_true', help='下载最新的UNII数据')
    parser.add_argument('--init', action='store_true', help='初始化数据库')
    parser.add_argument('--load', action='store_true', help='加载数据到数据库')
    parser.add_argument('--search-name', help='通过名称搜索UNII')
    parser.add_argument('--search-unii', help='通过UNII搜索物质信息')
    parser.add_argument('--info', action='store_true', help='显示数据库信息')
    parser.add_argument('--limit', type=int, default=10, help='搜索结果限制')
    
    args = parser.parse_args()
    
    db = UNIIDatabase()
    
    if args.init:
        db.init_database()
    
    if args.download:
        success = await db.download_latest_data()
        if success and args.load:
            db.load_data_to_database()
    
    if args.load and not args.download:
        db.load_data_to_database()
    
    if args.search_name:
        results = db.search_by_name(args.search_name, args.limit)
        if results:
            print(f"\n🔍 找到 {len(results)} 个结果:")
            for i, result in enumerate(results, 1):
                print(f"\n结果 {i}:")
                print(f"  UNII: {result['unii']}")
                print(f"  名称: {result['substance_name']}")
                print(f"  类别: {result['substance_class']}")
                if result['cas_number']:
                    print(f"  CAS: {result['cas_number']}")
        else:
            print("❌ 未找到匹配结果")
    
    if args.search_unii:
        result = db.search_by_unii(args.search_unii)
        if result:
            print(f"\n🔍 UNII信息:")
            print(f"  UNII: {result['unii']}")
            print(f"  名称: {result['substance_name']}")
            print(f"  类别: {result['substance_class']}")
            if result['cas_number']:
                print(f"  CAS: {result['cas_number']}")
            if result['molecular_formula']:
                print(f"  分子式: {result['molecular_formula']}")
        else:
            print("❌ 未找到匹配结果")
    
    if args.info:
        info = db.get_database_info()
        print(f"\n📊 数据库信息:")
        if info['status'] == 'exists':
            print(f"  状态: 已存在")
            print(f"  记录总数: {info['total_records']:,}")
            print(f"  最后更新: {info['last_update']}")
            print(f"  数据库大小: {info['database_size']}")
        else:
            print(f"  状态: 不存在")

if __name__ == "__main__":
    asyncio.run(main())
